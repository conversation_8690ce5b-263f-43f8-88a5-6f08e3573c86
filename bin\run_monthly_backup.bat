@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Monthly Backup System                             S4NG-7
REM ===============================================================
REM Unified monthly backup system with integrated functionality:
REM - Monthly backup operations (by month/year)
REM - Historical backup operations (by date range)
REM - Multiple backup strategies for maximum reliability
REM
REM This script runs complete monthly or historical backups using
REM the existing daily backup system. It processes data
REM sequentially for maximum reliability.
REM
REM Usage:
REM   run_monthly_backup.bat [mode] [parameters] [options]
REM
REM Modes:
REM   monthly            Monthly backup by month/year (default)
REM   historical         Historical backup by date range
REM   help               Show this help message
REM
REM Monthly Mode Examples:
REM   run_monthly_backup.bat march 2025
REM   run_monthly_backup.bat 3 2025
REM   run_monthly_backup.bat monthly march 2024 --dry-run
REM   run_monthly_backup.bat 3 2025 --start-day 15
REM
REM Historical Mode Examples:
REM   run_monthly_backup.bat historical 2025-03-01 2025-03-31
REM   run_monthly_backup.bat historical 2025-03-15 2025-03-15 --dry-run
REM   run_monthly_backup.bat historical 2025-03-01 2025-03-05 --verbose
REM
REM Monthly Options:
REM   --start-day DD     Start from specific day (default: 1)
REM   --end-day DD       End at specific day (default: last day of month)
REM   --dry-run          Validate only, no backup
REM   --verbose          Enable verbose logging
REM
REM Historical Options:
REM   --dry-run          Validate only, no backup
REM   --verbose          Enable verbose logging
REM   --single-table     Test with single table only
REM ===============================================================

REM Set the current directory to the project root (one level up from bin)
cd /d "%~dp0.."

REM Set default values
set MODE=monthly
set MONTH_NAME=%1
set YEAR=%2
set START_DAY=1
set END_DAY=
set DRY_RUN=false
set VERBOSE=false
set SINGLE_TABLE=false
set PYTHON_CMD=python

REM Set default paths (simplified approach like daily backup)
set SCRIPT_DIR=scripts
set LOG_BASE_DIR=logs
set TEMP_BASE_DIR=temp

REM Set script paths using configured directories
set HISTORICAL_BACKUP_SCRIPT=%SCRIPT_DIR%\historical_backup_processor.py

REM Basic input validation
if "%1"=="" goto help
if "%1"=="help" goto help
if "%1"=="--help" goto help

REM Process command line arguments and determine mode

REM Check if first argument is a mode
if "%1"=="monthly" (
    set MODE=monthly
    set MONTH_NAME=%2
    set YEAR=%3
    shift
    shift
    goto monthly_mode
)
if "%1"=="historical" (
    set MODE=historical
    set START_DATE=%2
    set END_DATE=%3
    shift
    shift
    goto historical_mode
)

REM Check if first argument looks like a date (YYYY-MM-DD)
echo %1 | findstr /r "^[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]$" >nul
if %ERRORLEVEL% EQU 0 (
    set MODE=historical
    set START_DATE=%1
    set END_DATE=%2
    shift
    goto historical_mode
)

REM Default to monthly mode
set MODE=monthly
goto monthly_mode

:monthly_mode
REM Simple month/year validation (like daily backup approach)
REM Get default year if not provided
if "%YEAR%"=="" set YEAR=2025

REM Convert month name to number
set MONTH_NUM=
if /i "%MONTH_NAME%"=="january" set MONTH_NUM=1
if /i "%MONTH_NAME%"=="february" set MONTH_NUM=2
if /i "%MONTH_NAME%"=="march" set MONTH_NUM=3
if /i "%MONTH_NAME%"=="april" set MONTH_NUM=4
if /i "%MONTH_NAME%"=="may" set MONTH_NUM=5
if /i "%MONTH_NAME%"=="june" set MONTH_NUM=6
if /i "%MONTH_NAME%"=="july" set MONTH_NUM=7
if /i "%MONTH_NAME%"=="august" set MONTH_NUM=8
if /i "%MONTH_NAME%"=="september" set MONTH_NUM=9
if /i "%MONTH_NAME%"=="october" set MONTH_NUM=10
if /i "%MONTH_NAME%"=="november" set MONTH_NUM=11
if /i "%MONTH_NAME%"=="december" set MONTH_NUM=12

REM If not a month name, try as number
if "%MONTH_NUM%"=="" (
    set MONTH_NUM=%MONTH_NAME%
    REM Basic validation for month number
    if %MONTH_NAME% LSS 1 goto invalid_month
    if %MONTH_NAME% GTR 12 goto invalid_month
)

REM Basic year validation
if %YEAR% LSS 2020 (
    echo ERROR: Year must be at least 2020, got: %YEAR%
    exit /b 1
)
if %YEAR% GTR 2030 (
    echo ERROR: Year cannot be greater than 2030, got: %YEAR%
    exit /b 1
)

REM Parse additional options
shift
shift
:parse_options
if "%1"=="" goto options_done
if "%1"=="--start-day" (
    set START_DAY=%2
    shift & shift & goto parse_options
)
if "%1"=="--end-day" (
    set END_DAY=%2
    shift & shift & goto parse_options
)
if "%1"=="--dry-run" (
    set DRY_RUN=true
    shift & goto parse_options
)
if "%1"=="--verbose" (
    set VERBOSE=true
    shift & goto parse_options
)
shift & goto parse_options

:options_done

REM Basic input range validation for START_DAY
if %START_DAY% LSS 1 (
    echo ERROR: Start day must be at least 1, got: %START_DAY%
    exit /b 1
)

REM Determine end day using simple calculation
if "%END_DAY%"=="" (
    REM Set default days for each month
    if %MONTH_NUM%==1 set END_DAY=31
    if %MONTH_NUM%==2 set END_DAY=28
    if %MONTH_NUM%==3 set END_DAY=31
    if %MONTH_NUM%==4 set END_DAY=30
    if %MONTH_NUM%==5 set END_DAY=31
    if %MONTH_NUM%==6 set END_DAY=30
    if %MONTH_NUM%==7 set END_DAY=31
    if %MONTH_NUM%==8 set END_DAY=31
    if %MONTH_NUM%==9 set END_DAY=30
    if %MONTH_NUM%==10 set END_DAY=31
    if %MONTH_NUM%==11 set END_DAY=30
    if %MONTH_NUM%==12 set END_DAY=31
)

REM Handle leap year for February separately
if "%END_DAY%"=="28" if %MONTH_NUM%==2 (
    set /a leap_check=%YEAR% %% 4
    if !leap_check!==0 (
        set /a leap_check_100=%YEAR% %% 100
        if !leap_check_100!==0 (
            set /a leap_check_400=%YEAR% %% 400
            if !leap_check_400!==0 (
                set END_DAY=29
            ) else (
                set END_DAY=28
            )
        ) else (
            set END_DAY=29
        )
    )
)

REM Validate END_DAY after it's been set
if %END_DAY% GTR 31 (
    echo ERROR: End day cannot be greater than 31, got: %END_DAY%
    exit /b 1
)
if %START_DAY% GTR %END_DAY% (
    echo ERROR: Start day ^(%START_DAY%^) cannot be greater than end day ^(%END_DAY%^)
    exit /b 1
)

echo ===============================================================
echo TNGD MONTHLY BACKUP SYSTEM
echo ===============================================================
echo Target: Month %MONTH_NUM% (%MONTH_NAME%) %YEAR%
echo Days: %START_DAY% to %END_DAY%
if "%DRY_RUN%"=="true" echo Mode: DRY RUN (validation only)
echo Current time: %date% %time%
echo ===============================================================
echo.

REM Check if tables.json exists
if not exist tabletest\tables.json (
    echo ERROR: tabletest\tables.json not found!
    echo The backup cannot proceed without the table list.
    exit /b 1
)

REM Create monthly log directory
REM Format month number with leading zero
set MONTH_FORMATTED=%MONTH_NUM%
if %MONTH_NUM% LSS 10 set MONTH_FORMATTED=0%MONTH_NUM%

REM Create log directory
set LOG_DIR=%LOG_BASE_DIR%\monthly\%YEAR%-%MONTH_FORMATTED%
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

REM Implement resume capability
set CHECKPOINT_FILE=%LOG_DIR%\checkpoint_%YEAR%_%MONTH_FORMATTED%.txt
set RESUME_FROM_DAY=%START_DAY%

REM Check for existing checkpoint and resume if found
if exist "%CHECKPOINT_FILE%" (
    set /p LAST_COMPLETED_DAY=<"%CHECKPOINT_FILE%"
    if not "!LAST_COMPLETED_DAY!"=="" (
        set /a RESUME_FROM_DAY=!LAST_COMPLETED_DAY!+1
        if !RESUME_FROM_DAY! LEQ %END_DAY% (
            echo Found checkpoint: Last completed day was !LAST_COMPLETED_DAY!
            echo Resuming from day !RESUME_FROM_DAY!
            echo.
        ) else (
            echo Checkpoint indicates all days already completed
            echo Removing checkpoint file and starting fresh
            del "%CHECKPOINT_FILE%" >nul 2>&1
            set RESUME_FROM_DAY=%START_DAY%
        )
    )
)

REM Initialize counters
set SUCCESSFUL_DAYS=0
set FAILED_DAYS=0
set TOTAL_DAYS=0

echo Starting monthly backup process...
echo Processing days %RESUME_FROM_DAY% to %END_DAY%...
echo.

REM Set delay between days (simple approach)
set DELAY_SECONDS=2

REM Process each day (starting from resume point)
for /l %%d in (%RESUME_FROM_DAY%,1,%END_DAY%) do (
    set /a TOTAL_DAYS+=1
    set DAY=%%d
    if !DAY! LSS 10 set DAY=0!DAY!

    REM CRITICAL FIX: Zero-pad month number for proper date format
    set MONTH_PADDED=%MONTH_NUM%
    if !MONTH_NUM! LSS 10 set MONTH_PADDED=0!MONTH_NUM!

    echo ===============================================================
    echo Processing Day %%d of Month %MONTH_NUM% ^(%YEAR%-!MONTH_PADDED!-!DAY!^)
    echo ===============================================================

    REM Build the specific date with zero-padded month
    set TARGET_DATE=%YEAR%-!MONTH_PADDED!-!DAY!

    REM Build command parameters for daily backup
    set FINAL_PARAMS=--date !TARGET_DATE!
    if "%DRY_RUN%"=="true" set FINAL_PARAMS=!FINAL_PARAMS! --dry-run
    if "%VERBOSE%"=="true" set FINAL_PARAMS=!FINAL_PARAMS! --verbose

    REM Run daily backup for this specific day with date parameter
    echo Running: run_daily_backup.bat !FINAL_PARAMS!
    echo Target date: !TARGET_DATE!
    echo.

    REM Execute with date parameter to ensure correct date processing
    echo [%date% %time%] Starting backup for day %%d
    call bin\run_daily_backup.bat !FINAL_PARAMS!
    set DAY_EXIT_CODE=!ERRORLEVEL!
    echo [%date% %time%] Backup for day %%d completed with exit code !DAY_EXIT_CODE!

    if !DAY_EXIT_CODE! EQU 0 (
        echo ✅ Day %%d completed successfully
        set /a SUCCESSFUL_DAYS+=1

        REM MEDIUM PRIORITY FIX: Update checkpoint for successful day
        echo %%d > "%CHECKPOINT_FILE%"
    ) else (
        echo ❌ Day %%d failed with exit code !DAY_EXIT_CODE!
        set /a FAILED_DAYS+=1

        REM Enhanced error recovery
        if not "%DRY_RUN%"=="true" (
            echo.
            echo Day %%d failed. You can choose to continue with remaining days.
            set /p CONTINUE_CHOICE="Continue with remaining days? (y/N): "
            if /i not "!CONTINUE_CHOICE!"=="y" (
                echo.
                echo Backup stopped by user after day %%d failure
                echo Checkpoint saved at day %%d for future resume
                echo %%d > "%CHECKPOINT_FILE%"
                goto end_processing
            )
            echo Continuing with remaining days...
            echo.
        )

        REM Update checkpoint even for failed days to avoid reprocessing
        echo %%d > "%CHECKPOINT_FILE%"
    )

    echo.
    echo Progress: !SUCCESSFUL_DAYS! successful, !FAILED_DAYS! failed out of !TOTAL_DAYS! processed
    echo.

    REM For dry-run, just process the first day to test
    if "%DRY_RUN%"=="true" (
        echo.
        echo DRY-RUN: Processing only first day for validation
        goto end_processing
    )

    REM Configurable delay between days
    if %DELAY_SECONDS% GTR 0 (
        echo Waiting %DELAY_SECONDS% seconds before next day...
        timeout /t %DELAY_SECONDS% /nobreak >nul
    )
)

:end_processing

echo ===============================================================
echo MONTHLY BACKUP COMPLETED
echo ===============================================================
echo Target: Month %MONTH_NUM% (%MONTH_NAME%) %YEAR%
echo Total days processed: %TOTAL_DAYS%
echo Successful days: %SUCCESSFUL_DAYS%
echo Failed days: %FAILED_DAYS%
echo Completion time: %date% %time%
echo ===============================================================

if %FAILED_DAYS% EQU 0 (
    echo ✅ Monthly backup completed successfully!
    echo All days were processed without errors.

    REM Clean up checkpoint file on successful completion
    if exist "%CHECKPOINT_FILE%" (
        del "%CHECKPOINT_FILE%" >nul 2>&1
        echo Checkpoint file cleaned up
    )
    exit /b 0
) else (
    echo ⚠️ Monthly backup completed with %FAILED_DAYS% failed days.
    echo Check individual day logs for details.
    echo Checkpoint file preserved for resume capability: %CHECKPOINT_FILE%
    exit /b 1
)

:invalid_month
echo ERROR: Invalid month '%MONTH_NAME%'. Must be 1-12 or month name.
exit /b 1

:historical_mode
echo ===============================================================
echo TNGD HISTORICAL BACKUP SYSTEM
echo ===============================================================
echo Start Date: %START_DATE%
echo End Date: %END_DATE%
echo Current time: %date% %time%
echo ===============================================================
echo.

REM Basic date validation for historical mode
if "%START_DATE%"=="" (
    echo ERROR: Start date is required for historical mode
    goto help
)

if "%END_DATE%"=="" (
    echo ERROR: End date is required for historical mode
    goto help
)

REM Basic date format validation
echo %START_DATE% | findstr /r "^[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]$" >nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Invalid start date format: %START_DATE%
    echo Expected format: YYYY-MM-DD
    exit /b 1
)

echo %END_DATE% | findstr /r "^[0-9][0-9][0-9][0-9]-[0-9][0-9]-[0-9][0-9]$" >nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Invalid end date format: %END_DATE%
    echo Expected format: YYYY-MM-DD
    exit /b 1
)

REM Parse additional options for historical mode
shift
:parse_historical_options
if "%1"=="" goto historical_options_done
if "%1"=="--dry-run" (
    set DRY_RUN=true
    shift & goto parse_historical_options
)
if "%1"=="--verbose" (
    set VERBOSE=true
    shift & goto parse_historical_options
)
if "%1"=="--single-table" (
    set SINGLE_TABLE=true
    shift & goto parse_historical_options
)
shift & goto parse_historical_options

:historical_options_done

REM Create logs directory
if not exist "%LOG_BASE_DIR%" mkdir "%LOG_BASE_DIR%"
if not exist "%LOG_BASE_DIR%\historical" mkdir "%LOG_BASE_DIR%\historical"

REM Create timestamped log file
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=!dt:~0,4!"
set "MM=!dt:~4,2!"
set "DD=!dt:~6,2!"
set "HH=!dt:~8,2!"
set "Min=!dt:~10,2!"
set "Sec=!dt:~12,2!"
set "LOG_FILE=%LOG_BASE_DIR%\historical\historical_backup_!YYYY!!MM!!DD!_!HH!!Min!!Sec!.log"

if "%DRY_RUN%"=="true" echo Mode: DRY RUN (validation only)
if "%SINGLE_TABLE%"=="true" echo Mode: SINGLE TABLE TEST
echo Log file: %LOG_FILE%
echo.

REM Check if tables.json exists
if not exist tabletest\tables.json (
    echo ERROR: tabletest\tables.json not found!
    echo The backup cannot proceed without the table list.
    exit /b 1
)

REM Check disk space and perform cleanup
echo Checking disk space and performing cleanup...
echo [%date% %time%] Starting disk cleanup check >> "%LOG_FILE%"

%PYTHON_CMD% utils\disk_cleanup.py --force --verbose >> "%LOG_FILE%" 2>&1
set CLEANUP_EXIT_CODE=%ERRORLEVEL%

if %CLEANUP_EXIT_CODE% NEQ 0 (
    echo ERROR: Disk cleanup failed or insufficient disk space for historical backup.
    echo Check log file for details: %LOG_FILE%
    echo [%date% %time%] Disk cleanup failed with exit code %CLEANUP_EXIT_CODE% >> "%LOG_FILE%"
    exit /b 1
)

echo [%date% %time%] Disk cleanup completed successfully >> "%LOG_FILE%"

REM Build command parameters
set PARAMS=--start-date %START_DATE% --end-date %END_DATE%
if "%DRY_RUN%"=="true" set PARAMS=%PARAMS% --dry-run
if "%VERBOSE%"=="true" set PARAMS=%PARAMS% --verbose
if "%SINGLE_TABLE%"=="true" set PARAMS=%PARAMS% --single-table

REM Enhanced logging
echo Starting historical backup process...
echo [%date% %time%] Historical backup started >> "%LOG_FILE%"
echo [%date% %time%] Start date: %START_DATE% >> "%LOG_FILE%"
echo [%date% %time%] End date: %END_DATE% >> "%LOG_FILE%"
if "%DRY_RUN%"=="true" echo [%date% %time%] Mode: DRY RUN >> "%LOG_FILE%"
echo.

REM Execute historical backup
%PYTHON_CMD% %HISTORICAL_BACKUP_SCRIPT% %PARAMS% >> "%LOG_FILE%" 2>&1
set BACKUP_EXIT_CODE=%ERRORLEVEL%

echo.
echo ===============================================================
echo HISTORICAL BACKUP COMPLETED at %date% %time%
echo Exit code: %BACKUP_EXIT_CODE%
echo ===============================================================

REM Enhanced completion logging
echo [%date% %time%] Historical backup completed with exit code %BACKUP_EXIT_CODE% >> "%LOG_FILE%"

if %BACKUP_EXIT_CODE% EQU 0 (
    echo ✅ Historical backup completed successfully!
    echo [%date% %time%] Backup completed successfully >> "%LOG_FILE%"
) else (
    echo ❌ Historical backup failed or completed with errors.
    echo Check the log file for details: %LOG_FILE%
    echo [%date% %time%] Backup failed - check logs for details >> "%LOG_FILE%"
)

echo.
echo Log file: %LOG_FILE%
echo.

REM Show recent log entries for quick review
echo ===============================================================
echo RECENT LOG ENTRIES (Last 20 lines):
echo ===============================================================
if exist "%LOG_FILE%" (
    powershell -Command "Get-Content '%LOG_FILE%' | Select-Object -Last 20"
) else (
    echo Log file not found.
)
echo ===============================================================

exit /b %BACKUP_EXIT_CODE%

:help
echo ===============================================================
echo TNGD Monthly Backup System - Unified Help
echo ===============================================================
echo.
echo This is the unified monthly backup system that consolidates
echo monthly and historical backup functionality into a single interface.
echo.
echo Usage:
echo   run_monthly_backup.bat [mode] [parameters] [options]
echo.
echo MODES:
echo ===============================================================
echo.
echo 1. MONTHLY MODE (default)
echo   run_monthly_backup.bat [monthly] [month] [year] [options]
echo
echo   Parameters:
echo     month              Month name (january, february, march, etc.) or number (1-12)
echo     year               Year (e.g., 2025)
echo
echo   Options:
echo     --start-day DD     Start from specific day (default: 1)
echo     --end-day DD       End at specific day (default: last day of month)
echo     --dry-run          Validate only, no backup
echo     --verbose          Enable verbose logging
echo.
echo   Examples:
echo     run_monthly_backup.bat march 2025
echo     run_monthly_backup.bat 3 2025
echo     run_monthly_backup.bat monthly march 2024 --dry-run
echo     run_monthly_backup.bat 3 2025 --start-day 15 --end-day 20
echo     run_monthly_backup.bat february 2024 --verbose
echo.
echo 2. HISTORICAL MODE
echo   run_monthly_backup.bat historical [start-date] [end-date] [options]
echo
echo   Parameters:
echo     start-date         Start date in YYYY-MM-DD format
echo     end-date           End date in YYYY-MM-DD format
echo
echo   Options:
echo     --dry-run          Validate only, no backup
echo     --verbose          Enable verbose logging
echo     --single-table     Test with single table only
echo.
echo   Examples:
echo     run_monthly_backup.bat historical 2025-03-01 2025-03-31
echo     run_monthly_backup.bat historical 2025-03-15 2025-03-15 --dry-run
echo     run_monthly_backup.bat historical 2025-03-01 2025-03-05 --verbose
echo     run_monthly_backup.bat 2025-03-01 2025-03-31
echo.
echo ===============================================================
echo FEATURES:
echo ===============================================================
echo   ✅ Day-by-day sequential processing for maximum reliability
echo   ✅ Uses existing daily backup infrastructure
echo   ✅ Comprehensive error handling and retry mechanism
echo   ✅ Progress tracking and email notifications
echo   ✅ Resume capability from last successful day
echo   ✅ Multiple backup strategies available
echo   ✅ Historical date range backup support
echo   ✅ Automatic disk space management
echo   ✅ Configurable OSS path structure: Devo/{month}/week {n}/{date}/
echo.
echo ===============================================================
echo BACKUP STRATEGIES:
echo ===============================================================
echo 1. Day-by-Day Sequential (Recommended)
echo    - Maximum reliability and easy resumption
echo    - Each day processed independently
echo    - Minimal resource usage
echo.
echo 2. Week-by-Week Chunked
echo    - Balance between efficiency and reliability
echo    - Weekly data chunks
echo    - Faster than day-by-day
echo.
echo 3. Hybrid Adaptive
echo    - Adapts to table characteristics
echo    - Optimized chunk sizes
echo    - Best for diverse datasets
echo.
echo 4. Emergency Fallback
echo    - Maximum safety mode
echo    - Conservative settings with maximum retries
echo    - Used when other strategies fail
echo.
echo ===============================================================
echo QUICK START:
echo ===============================================================
echo 1. Test monthly backup:     run_monthly_backup.bat march 2025 --dry-run
echo 2. Run monthly backup:      run_monthly_backup.bat march 2025
echo 3. Test historical backup:  run_monthly_backup.bat historical 2025-03-01 2025-03-31 --dry-run
echo 4. Run historical backup:   run_monthly_backup.bat historical 2025-03-01 2025-03-31
echo.
echo This script uses the existing daily backup system to process
echo data sequentially for maximum reliability. Each day/period is
echo processed as a separate backup operation with full error handling.
echo.
echo ===============================================================
exit /b 0
